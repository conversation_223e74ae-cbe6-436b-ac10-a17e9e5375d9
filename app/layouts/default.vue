<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui';

// Navigation items for the NavigationMenu
const navigationItems = ref<NavigationMenuItem[]>([
    {
        label: 'Dashboard',
        icon: 'i-lucide-layout-dashboard',
        to: '/',
    },
    {
        label: 'Projects',
        icon: 'i-lucide-folder',
        to: '/projects',
    },
    {
        label: 'Analytics',
        icon: 'i-lucide-chart-line',
        to: '/analytics',
    },
    {
        label: 'Settings',
        icon: 'i-lucide-settings',
        to: '/settings',
    },
    {
        label: 'Help',
        icon: 'i-lucide-circle-help',
        to: '/help',
    },
]);

// Sidebar state
const isSidebarOpen = ref(true);
const isCollapsed = computed(() => !isSidebarOpen.value);
</script>

<template>
    <div class="flex flex-col min-h-screen">
        <!-- Header -->
        <div
            class="h-16 w-full flex items-center justify-between gap-4 px-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900"
        >
            <div class="flex items-center gap-4">
                <UButton
                    icon="i-lucide-menu"
                    color="neutral"
                    variant="ghost"
                    size="sm"
                    square
                    class="lg:hidden"
                    @click="isSidebarOpen = !isSidebarOpen"
                />
                <UIcon
                    name="i-custom-sensehawk-logo"
                    class="h-8 w-8 animate-fade-in"
                />
            </div>
            <div class="flex items-center gap-2">
                <UserMenu />
            </div>
        </div>

        <!-- Main content area with sidebar -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar with NavigationMenu -->
            <div
                :class="[
                    'transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800',
                    isSidebarOpen ? 'w-64' : 'w-0 lg:w-16',
                    'lg:relative absolute z-10 h-full',
                ]"
            >
                <div class="flex flex-col h-[calc(100vh-4rem)]">
                    <!-- Navigation Menu -->
                    <div class="flex-1 px-4 py-6">
                        <UNavigationMenu
                            :items="navigationItems"
                            orientation="vertical"
                            :collapsed="isCollapsed"
                            highlight
                            class="h-full"
                        />
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="flex-1 overflow-auto">
                <slot />
            </div>
        </div>

        <!-- Overlay for mobile when sidebar is open -->
        <div
            v-if="isSidebarOpen"
            class="fixed inset-0 bg-black bg-opacity-50 z-0 lg:hidden"
            @click="isSidebarOpen = false"
        />
    </div>
</template>
