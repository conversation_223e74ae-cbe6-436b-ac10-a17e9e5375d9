<script setup lang="ts">
// Sidebar navigation items
const sidebarItems = [
    {
        label: 'Dashboard',
        icon: 'i-lucide-layout-dashboard',
        to: '/',
    },
    {
        label: 'Projects',
        icon: 'i-lucide-folder',
        to: '/projects',
    },
    {
        label: 'Analytics',
        icon: 'i-lucide-chart-line',
        to: '/analytics',
    },
    {
        label: 'Settings',
        icon: 'i-lucide-settings',
        to: '/settings',
    },
    {
        label: 'Help',
        icon: 'i-lucide-circle-help',
        to: '/help',
    },
];

// Sidebar state
const isSidebarOpen = ref(true);
</script>

<template>
    <div class="flex flex-col min-h-screen">
        <!-- Header -->
        <div
            class="h-16 w-full flex items-center justify-between gap-4 px-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900"
        >
            <div class="flex items-center gap-4">
                <UButton
                    icon="i-lucide-menu"
                    color="neutral"
                    variant="ghost"
                    size="sm"
                    square
                    class="lg:hidden"
                    @click="isSidebarOpen = !isSidebarOpen"
                />
                <UIcon
                    name="i-custom-sensehawk-logo"
                    class="h-8 w-8 animate-fade-in"
                />
            </div>
            <div class="flex items-center gap-2">
                <UserMenu />
            </div>
        </div>

        <!-- Main content area with sidebar -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar -->
            <div
                :class="[
                    'transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800',
                    isSidebarOpen ? 'w-64' : 'w-0 lg:w-16',
                    'lg:relative absolute z-10 h-full',
                ]"
            >
                <div class="flex flex-col h-[calc(100vh-4rem)]">
                    <!-- Sidebar content -->
                    <nav class="flex-1 px-4 py-6 space-y-2 h-full">
                        <NuxtLink
                            v-for="item in sidebarItems"
                            :key="item.to"
                            :to="item.to"
                            class="flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                            active-class="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                        >
                            <UIcon
                                :name="item.icon"
                                class="h-5 w-5 flex-shrink-0"
                            />
                            <span
                                v-show="isSidebarOpen || !isSidebarOpen"
                                :class="[
                                    'transition-opacity duration-200',
                                    isSidebarOpen ? 'opacity-100' : 'lg:opacity-0 opacity-100',
                                ]"
                            >
                                {{ item.label }}
                            </span>
                        </NuxtLink>
                    </nav>
                </div>
            </div>

            <!-- Main content -->
            <div class="flex-1 overflow-auto">
                <slot />
            </div>
        </div>

        <!-- Overlay for mobile when sidebar is open -->
        <div
            v-if="isSidebarOpen"
            class="fixed inset-0 bg-black bg-opacity-50 z-0 lg:hidden"
            @click="isSidebarOpen = false"
        />
    </div>
</template>
